  Activity android.app  
MainScreen android.app.Activity  ZuijijiTheme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  
MainScreen android.content.Context  ZuijijiTheme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  
MainScreen android.content.ContextWrapper  ZuijijiTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
MainScreen  android.view.ContextThemeWrapper  ZuijijiTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  ZuijijiTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
MainScreen -androidx.activity.ComponentActivity.Companion  ZuijijiTheme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
BlurDialog "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  ImageRequest "androidx.compose.foundation.layout  LocalContext "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  ZuijijiTheme "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  blur "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberAsyncImagePainter "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  wrapContentHeight "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  
BlurDialog +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  ImageRequest +androidx.compose.foundation.layout.BoxScope  LocalContext +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  blur +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  
cardElevation +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  rememberAsyncImagePainter +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  wrapContentHeight +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  Color +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  RoundedCornerShape !androidx.compose.foundation.shape  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
BlurDialog androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Image androidx.compose.material3  ImageRequest androidx.compose.material3  LocalContext androidx.compose.material3  
MainScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  ZuijijiTheme androidx.compose.material3  align androidx.compose.material3  blur androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberAsyncImagePainter androidx.compose.material3  setValue androidx.compose.material3  spacedBy androidx.compose.material3  wrapContentHeight androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
BlurDialog androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Image androidx.compose.runtime  ImageRequest androidx.compose.runtime  LocalContext androidx.compose.runtime  
MainScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Text androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  ZuijijiTheme androidx.compose.runtime  align androidx.compose.runtime  blur androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  
cardElevation androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberAsyncImagePainter androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  wrapContentHeight androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  blur androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  wrapContentHeight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  blur androidx.compose.ui.draw  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  ZuijijiTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AsyncImagePainter coil.compose  rememberAsyncImagePainter coil.compose  ImageRequest coil.request  Builder coil.request.ImageRequest  build !coil.request.ImageRequest.Builder  data !coil.request.ImageRequest.Builder  ActivityResultContracts com.example.zuijiji  	Alignment com.example.zuijiji  Arrangement com.example.zuijiji  
BlurDialog com.example.zuijiji  Box com.example.zuijiji  Bundle com.example.zuijiji  Button com.example.zuijiji  ButtonDefaults com.example.zuijiji  Card com.example.zuijiji  CardDefaults com.example.zuijiji  Color com.example.zuijiji  Column com.example.zuijiji  ComponentActivity com.example.zuijiji  
Composable com.example.zuijiji  ContentScale com.example.zuijiji  Image com.example.zuijiji  ImageRequest com.example.zuijiji  LocalContext com.example.zuijiji  MainActivity com.example.zuijiji  
MainScreen com.example.zuijiji  
MaterialTheme com.example.zuijiji  Modifier com.example.zuijiji  RoundedCornerShape com.example.zuijiji  Text com.example.zuijiji  Unit com.example.zuijiji  Uri com.example.zuijiji  ZuijijiTheme com.example.zuijiji  align com.example.zuijiji  blur com.example.zuijiji  buttonColors com.example.zuijiji  
cardColors com.example.zuijiji  
cardElevation com.example.zuijiji  fillMaxSize com.example.zuijiji  fillMaxWidth com.example.zuijiji  getValue com.example.zuijiji  let com.example.zuijiji  mutableStateOf com.example.zuijiji  padding com.example.zuijiji  provideDelegate com.example.zuijiji  remember com.example.zuijiji  rememberAsyncImagePainter com.example.zuijiji  setValue com.example.zuijiji  spacedBy com.example.zuijiji  wrapContentHeight com.example.zuijiji  
MainScreen  com.example.zuijiji.MainActivity  ZuijijiTheme  com.example.zuijiji.MainActivity  enableEdgeToEdge  com.example.zuijiji.MainActivity  
setContent  com.example.zuijiji.MainActivity  Boolean com.example.zuijiji.ui.theme  Build com.example.zuijiji.ui.theme  
Composable com.example.zuijiji.ui.theme  DarkColorScheme com.example.zuijiji.ui.theme  
FontFamily com.example.zuijiji.ui.theme  
FontWeight com.example.zuijiji.ui.theme  LightColorScheme com.example.zuijiji.ui.theme  Pink40 com.example.zuijiji.ui.theme  Pink80 com.example.zuijiji.ui.theme  Purple40 com.example.zuijiji.ui.theme  Purple80 com.example.zuijiji.ui.theme  PurpleGrey40 com.example.zuijiji.ui.theme  PurpleGrey80 com.example.zuijiji.ui.theme  
Typography com.example.zuijiji.ui.theme  Unit com.example.zuijiji.ui.theme  ZuijijiTheme com.example.zuijiji.ui.theme  	Function0 kotlin  	Function1 kotlin  let kotlin  sp 
kotlin.Double  	compareTo 
kotlin.Int  KMutableProperty0 kotlin.reflect  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  zIndex "androidx.compose.foundation.layout  Box +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  zIndex +androidx.compose.foundation.layout.BoxScope  
background androidx.compose.material3  clip androidx.compose.material3  zIndex androidx.compose.material3  
background androidx.compose.runtime  clip androidx.compose.runtime  zIndex androidx.compose.runtime  zIndex androidx.compose.ui  clip androidx.compose.ui.Modifier  zIndex androidx.compose.ui.Modifier  clip androidx.compose.ui.draw  
background com.example.zuijiji  clip com.example.zuijiji  zIndex com.example.zuijiji  detectDragGestures $androidx.compose.foundation.gestures  	IntOffset "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  pointerInput "androidx.compose.foundation.layout  
roundToInt "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  	IntOffset +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Unit +androidx.compose.foundation.layout.BoxScope  detectDragGestures +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  
plusAssign +androidx.compose.foundation.layout.BoxScope  pointerInput +androidx.compose.foundation.layout.BoxScope  
roundToInt +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  Spacer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  	IntOffset androidx.compose.material3  Offset androidx.compose.material3  Spacer androidx.compose.material3  height androidx.compose.material3  offset androidx.compose.material3  
plusAssign androidx.compose.material3  pointerInput androidx.compose.material3  
roundToInt androidx.compose.material3  size androidx.compose.material3  	IntOffset androidx.compose.runtime  Offset androidx.compose.runtime  Spacer androidx.compose.runtime  height androidx.compose.runtime  offset androidx.compose.runtime  
plusAssign androidx.compose.runtime  pointerInput androidx.compose.runtime  
roundToInt androidx.compose.runtime  size androidx.compose.runtime  height androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  height &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  Offset androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  plus #androidx.compose.ui.geometry.Offset  
plusAssign #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  detectDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  
plusAssign 3androidx.compose.ui.input.pointer.PointerInputScope  Density androidx.compose.ui.unit  	IntOffset androidx.compose.ui.unit  	IntOffset  androidx.compose.ui.unit.Density  
roundToInt  androidx.compose.ui.unit.Density  	IntOffset com.example.zuijiji  Offset com.example.zuijiji  Spacer com.example.zuijiji  height com.example.zuijiji  offset com.example.zuijiji  
plusAssign com.example.zuijiji  pointerInput com.example.zuijiji  
roundToInt com.example.zuijiji  size com.example.zuijiji  	Function2 kotlin  
roundToInt kotlin.Float  
plusAssign kotlin.collections  SuspendFunction1 kotlin.coroutines  
roundToInt kotlin.math  detectTapGestures $androidx.compose.foundation.gestures  Brush "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  linearGradient "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  radialGradient "androidx.compose.foundation.layout  Brush +androidx.compose.foundation.layout.BoxScope  
graphicsLayer +androidx.compose.foundation.layout.BoxScope  linearGradient +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  radialGradient +androidx.compose.foundation.layout.BoxScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  Brush androidx.compose.material3  
graphicsLayer androidx.compose.material3  linearGradient androidx.compose.material3  listOf androidx.compose.material3  radialGradient androidx.compose.material3  Brush androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  linearGradient androidx.compose.runtime  listOf androidx.compose.runtime  radialGradient androidx.compose.runtime  
graphicsLayer androidx.compose.ui.Modifier  
graphicsLayer &androidx.compose.ui.Modifier.Companion  Brush androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  linearGradient "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  linearGradient ,androidx.compose.ui.graphics.Brush.Companion  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  Transparent "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  shadowElevation /androidx.compose.ui.graphics.GraphicsLayerScope  detectTapGestures 3androidx.compose.ui.input.pointer.PointerInputScope  Brush com.example.zuijiji  
graphicsLayer com.example.zuijiji  linearGradient com.example.zuijiji  listOf com.example.zuijiji  radialGradient com.example.zuijiji  invoke kotlin.Function0  List kotlin.collections  listOf kotlin.collections  ActivityResultLauncher androidx.activity.result  launch /androidx.activity.result.ActivityResultLauncher  BackgroundLayer "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  RealTimeBlurDialog "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  BackgroundLayer +androidx.compose.foundation.layout.BoxScope  RealTimeBlurDialog +androidx.compose.foundation.layout.BoxScope  Box .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  activity +androidx.compose.foundation.layout.androidx  result 4androidx.compose.foundation.layout.androidx.activity  ActivityResultLauncher ;androidx.compose.foundation.layout.androidx.activity.result  BackgroundLayer androidx.compose.material3  Boolean androidx.compose.material3  LaunchedEffect androidx.compose.material3  RealTimeBlurDialog androidx.compose.material3  String androidx.compose.material3  androidx androidx.compose.material3  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  activity #androidx.compose.material3.androidx  result ,androidx.compose.material3.androidx.activity  ActivityResultLauncher 3androidx.compose.material3.androidx.activity.result  BackgroundLayer androidx.compose.runtime  Boolean androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  RealTimeBlurDialog androidx.compose.runtime  String androidx.compose.runtime  androidx androidx.compose.runtime  activity !androidx.compose.runtime.androidx  result *androidx.compose.runtime.androidx.activity  ActivityResultLauncher 1androidx.compose.runtime.androidx.activity.result  BackgroundLayer com.example.zuijiji  Boolean com.example.zuijiji  LaunchedEffect com.example.zuijiji  RealTimeBlurDialog com.example.zuijiji  String com.example.zuijiji  androidx com.example.zuijiji  activity com.example.zuijiji.androidx  result %com.example.zuijiji.androidx.activity  ActivityResultLauncher ,com.example.zuijiji.androidx.activity.result  invoke kotlin.Function1  
unaryMinus 
kotlin.Int  CoroutineScope kotlinx.coroutines  layout "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  layout +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  width .androidx.compose.foundation.layout.ColumnScope  layout androidx.compose.material3  width androidx.compose.material3  layout androidx.compose.runtime  width androidx.compose.runtime  layout androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  layout &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  
Measurable androidx.compose.ui.layout  
MeasureResult androidx.compose.ui.layout  MeasureScope androidx.compose.ui.layout  	Placeable androidx.compose.ui.layout  layout androidx.compose.ui.layout  measure %androidx.compose.ui.layout.Measurable  layout 'androidx.compose.ui.layout.MeasureScope  
roundToInt 'androidx.compose.ui.layout.MeasureScope  PlacementScope $androidx.compose.ui.layout.Placeable  place $androidx.compose.ui.layout.Placeable  place 3androidx.compose.ui.layout.Placeable.PlacementScope  
roundToInt 3androidx.compose.ui.layout.Placeable.PlacementScope  LocalDensity androidx.compose.ui.platform  Constraints androidx.compose.ui.unit  copy $androidx.compose.ui.unit.Constraints  	maxHeight $androidx.compose.ui.unit.Constraints  maxWidth $androidx.compose.ui.unit.Constraints  layout com.example.zuijiji  width com.example.zuijiji  	Function3 kotlin  div 
kotlin.Int  minus 
kotlin.Int  times 
kotlin.Int  Nothing kotlin  Offset +androidx.compose.foundation.layout.BoxScope  Offset 3androidx.compose.ui.input.pointer.PointerInputScope  plus kotlin.Float  
Configuration android.content.res  screenHeightDp !android.content.res.Configuration  
screenWidthDp !android.content.res.Configuration  LocalConfiguration androidx.compose.ui.platform  value androidx.compose.ui.unit.Dp  div kotlin.Float  minus kotlin.Float  dp 
kotlin.Int  plus 
kotlin.Int  with "androidx.compose.foundation.layout  with androidx.compose.material3  with androidx.compose.runtime  dp  androidx.compose.ui.unit.Density  toPx  androidx.compose.ui.unit.Density  toPx androidx.compose.ui.unit.Dp  with com.example.zuijiji  with kotlin  	BottomEnd androidx.compose.ui.Alignment  BottomStart androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  BottomStart 'androidx.compose.ui.Alignment.Companion  border androidx.compose.foundation  border "androidx.compose.foundation.layout  border +androidx.compose.foundation.layout.BoxScope  border androidx.compose.material3  border androidx.compose.runtime  border androidx.compose.ui.Modifier  border com.example.zuijiji  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  ColorFilter "androidx.compose.foundation.layout  ColorMatrix "androidx.compose.foundation.layout  colorMatrix "androidx.compose.foundation.layout  floatArrayOf "androidx.compose.foundation.layout  ColorFilter +androidx.compose.foundation.layout.BoxScope  ColorMatrix +androidx.compose.foundation.layout.BoxScope  colorMatrix +androidx.compose.foundation.layout.BoxScope  floatArrayOf +androidx.compose.foundation.layout.BoxScope  ColorFilter androidx.compose.material3  ColorMatrix androidx.compose.material3  colorMatrix androidx.compose.material3  floatArrayOf androidx.compose.material3  ColorFilter androidx.compose.runtime  ColorMatrix androidx.compose.runtime  colorMatrix androidx.compose.runtime  floatArrayOf androidx.compose.runtime  ColorFilter androidx.compose.ui.graphics  ColorMatrix androidx.compose.ui.graphics  	Companion (androidx.compose.ui.graphics.ColorFilter  colorMatrix (androidx.compose.ui.graphics.ColorFilter  colorMatrix 2androidx.compose.ui.graphics.ColorFilter.Companion  ColorFilter com.example.zuijiji  ColorMatrix com.example.zuijiji  colorMatrix com.example.zuijiji  floatArrayOf com.example.zuijiji  
FloatArray kotlin  floatArrayOf kotlin  
unaryMinus kotlin.Float  Cyan "androidx.compose.ui.graphics.Color  Cyan ,androidx.compose.ui.graphics.Color.Companion  
sweepGradient "androidx.compose.foundation.layout  
sweepGradient +androidx.compose.foundation.layout.BoxScope  
sweepGradient androidx.compose.material3  
sweepGradient androidx.compose.runtime  
sweepGradient "androidx.compose.ui.graphics.Brush  
sweepGradient ,androidx.compose.ui.graphics.Brush.Companion  Blue "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Magenta "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Magenta ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  
sweepGradient com.example.zuijiji  ActivityResultContracts androidx.compose.animation.core  	Alignment androidx.compose.animation.core  BackgroundLayer androidx.compose.animation.core  Boolean androidx.compose.animation.core  Box androidx.compose.animation.core  Bundle androidx.compose.animation.core  Button androidx.compose.animation.core  Color androidx.compose.animation.core  ColorFilter androidx.compose.animation.core  ColorMatrix androidx.compose.animation.core  ComponentActivity androidx.compose.animation.core  
Composable androidx.compose.animation.core  ContentScale androidx.compose.animation.core  Easing androidx.compose.animation.core  Image androidx.compose.animation.core  ImageRequest androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  	IntOffset androidx.compose.animation.core  LaunchedEffect androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  LiquidGlassDialog androidx.compose.animation.core  LocalContext androidx.compose.animation.core  
MainScreen androidx.compose.animation.core  Modifier androidx.compose.animation.core  Offset androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  RoundedCornerShape androidx.compose.animation.core  String androidx.compose.animation.core  Text androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  Unit androidx.compose.animation.core  Uri androidx.compose.animation.core  ZuijijiTheme androidx.compose.animation.core  align androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  
background androidx.compose.animation.core  blur androidx.compose.animation.core  clip androidx.compose.animation.core  colorMatrix androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  floatArrayOf androidx.compose.animation.core  getValue androidx.compose.animation.core  height androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  layout androidx.compose.animation.core  let androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  offset androidx.compose.animation.core  padding androidx.compose.animation.core  
plusAssign androidx.compose.animation.core  pointerInput androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  rememberAsyncImagePainter androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  
roundToInt androidx.compose.animation.core  setValue androidx.compose.animation.core  tween androidx.compose.animation.core  width androidx.compose.animation.core  with androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  Reverse *androidx.compose.animation.core.RepeatMode  activity (androidx.compose.animation.core.androidx  result 1androidx.compose.animation.core.androidx.activity  ActivityResultLauncher 8androidx.compose.animation.core.androidx.activity.result  LinearEasing "androidx.compose.foundation.layout  LiquidGlassDialog "androidx.compose.foundation.layout  
RepeatMode "androidx.compose.foundation.layout  animateFloat "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  rememberInfiniteTransition "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  LiquidGlassDialog +androidx.compose.foundation.layout.BoxScope  LinearEasing androidx.compose.material3  LiquidGlassDialog androidx.compose.material3  
RepeatMode androidx.compose.material3  animateFloat androidx.compose.material3  infiniteRepeatable androidx.compose.material3  rememberInfiniteTransition androidx.compose.material3  tween androidx.compose.material3  LinearEasing androidx.compose.runtime  LiquidGlassDialog androidx.compose.runtime  
RepeatMode androidx.compose.runtime  State androidx.compose.runtime  animateFloat androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  tween androidx.compose.runtime  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  shadow androidx.compose.ui.draw  LinearEasing com.example.zuijiji  LiquidGlassDialog com.example.zuijiji  
RepeatMode com.example.zuijiji  animateFloat com.example.zuijiji  infiniteRepeatable com.example.zuijiji  rememberInfiniteTransition com.example.zuijiji  tween com.example.zuijiji  sin kotlin.math  
KProperty0 kotlin.reflect  Brush androidx.compose.animation.core  border androidx.compose.animation.core  linearGradient androidx.compose.animation.core  listOf androidx.compose.animation.core  radialGradient androidx.compose.animation.core  shadow androidx.compose.animation.core  sin androidx.compose.animation.core  shadow "androidx.compose.foundation.layout  sin "androidx.compose.foundation.layout  shadow +androidx.compose.foundation.layout.BoxScope  sin +androidx.compose.foundation.layout.BoxScope  shadow androidx.compose.material3  sin androidx.compose.material3  shadow androidx.compose.runtime  sin androidx.compose.runtime  shadow androidx.compose.ui.Modifier  shadow com.example.zuijiji  sin com.example.zuijiji  times kotlin.Float  CircleShape "androidx.compose.foundation.layout  CircleShape +androidx.compose.foundation.layout.BoxScope  CircleShape !androidx.compose.foundation.shape  CircleShape androidx.compose.material3  CircleShape androidx.compose.runtime  RenderEffect androidx.compose.ui.graphics  Shader androidx.compose.ui.graphics  TileMode androidx.compose.ui.graphics  CircleShape com.example.zuijiji  abs kotlin.math  sqrt kotlin.math  Canvas androidx.compose.foundation  Canvas "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  LensRefractionEffect "androidx.compose.foundation.layout  PI "androidx.compose.foundation.layout  cos "androidx.compose.foundation.layout  forEachIndexed "androidx.compose.foundation.layout  invoke "androidx.compose.foundation.layout  step "androidx.compose.foundation.layout  until "androidx.compose.foundation.layout  Canvas +androidx.compose.foundation.layout.BoxScope  LensRefractionEffect +androidx.compose.foundation.layout.BoxScope  PI +androidx.compose.foundation.layout.BoxScope  cos +androidx.compose.foundation.layout.BoxScope  forEachIndexed +androidx.compose.foundation.layout.BoxScope  invoke +androidx.compose.foundation.layout.BoxScope  step +androidx.compose.foundation.layout.BoxScope  until +androidx.compose.foundation.layout.BoxScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  unit 6androidx.compose.foundation.layout.androidx.compose.ui  Dp ;androidx.compose.foundation.layout.androidx.compose.ui.unit  Canvas androidx.compose.material3  Float androidx.compose.material3  LensRefractionEffect androidx.compose.material3  PI androidx.compose.material3  cos androidx.compose.material3  forEachIndexed androidx.compose.material3  invoke androidx.compose.material3  step androidx.compose.material3  until androidx.compose.material3  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  unit .androidx.compose.material3.androidx.compose.ui  Dp 3androidx.compose.material3.androidx.compose.ui.unit  Canvas androidx.compose.runtime  Float androidx.compose.runtime  LensRefractionEffect androidx.compose.runtime  PI androidx.compose.runtime  cos androidx.compose.runtime  forEachIndexed androidx.compose.runtime  invoke androidx.compose.runtime  step androidx.compose.runtime  until androidx.compose.runtime  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  unit ,androidx.compose.runtime.androidx.compose.ui  Dp 1androidx.compose.runtime.androidx.compose.ui.unit  Size androidx.compose.ui.geometry  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  ImageBitmap androidx.compose.ui.graphics  Paint androidx.compose.ui.graphics  asComposeRenderEffect androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  nativeCanvas androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  cameraDistance /androidx.compose.ui.graphics.GraphicsLayerScope  cos /androidx.compose.ui.graphics.GraphicsLayerScope  	rotationX /androidx.compose.ui.graphics.GraphicsLayerScope  	rotationY /androidx.compose.ui.graphics.GraphicsLayerScope  	rotationZ /androidx.compose.ui.graphics.GraphicsLayerScope  scaleX /androidx.compose.ui.graphics.GraphicsLayerScope  scaleY /androidx.compose.ui.graphics.GraphicsLayerScope  sin /androidx.compose.ui.graphics.GraphicsLayerScope  	DrawScope &androidx.compose.ui.graphics.drawscope  drawIntoCanvas &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  PI 0androidx.compose.ui.graphics.drawscope.DrawScope  cos 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed 0androidx.compose.ui.graphics.drawscope.DrawScope  invoke 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  sin 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  step 0androidx.compose.ui.graphics.drawscope.DrawScope  until 0androidx.compose.ui.graphics.drawscope.DrawScope  cos 'androidx.compose.ui.layout.MeasureScope  sin 'androidx.compose.ui.layout.MeasureScope  cos 3androidx.compose.ui.layout.Placeable.PlacementScope  sin 3androidx.compose.ui.layout.Placeable.PlacementScope  Canvas com.example.zuijiji  Float com.example.zuijiji  LensRefractionEffect com.example.zuijiji  PI com.example.zuijiji  cos com.example.zuijiji  forEachIndexed com.example.zuijiji  invoke com.example.zuijiji  step com.example.zuijiji  until com.example.zuijiji  compose com.example.zuijiji.androidx  ui $com.example.zuijiji.androidx.compose  unit 'com.example.zuijiji.androidx.compose.ui  Dp ,com.example.zuijiji.androidx.compose.ui.unit  div 
kotlin.Double  dp 
kotlin.Double  toFloat 
kotlin.Double  invoke 
kotlin.Int  IntIterator kotlin.collections  forEachIndexed kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  forEachIndexed kotlin.collections.List  PI kotlin.math  cos kotlin.math  pow kotlin.math  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  step 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  step kotlin.ranges.IntRange  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  coerceIn "androidx.compose.foundation.layout  sqrt "androidx.compose.foundation.layout  coerceIn +androidx.compose.foundation.layout.BoxScope  sqrt +androidx.compose.foundation.layout.BoxScope  coerceIn androidx.compose.material3  sqrt androidx.compose.material3  coerceIn androidx.compose.runtime  sqrt androidx.compose.runtime  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  coerceIn 'androidx.compose.ui.layout.MeasureScope  sqrt 'androidx.compose.ui.layout.MeasureScope  coerceIn 3androidx.compose.ui.layout.Placeable.PlacementScope  sqrt 3androidx.compose.ui.layout.Placeable.PlacementScope  coerceIn com.example.zuijiji  sqrt com.example.zuijiji  coerceIn kotlin.Float  coerceIn 
kotlin.ranges  iterator kotlin.ranges.IntRange  EdgeMagnificationEffect "androidx.compose.foundation.layout  Paint "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  EdgeMagnificationEffect +androidx.compose.foundation.layout.BoxScope  Paint +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  drawIntoCanvas +androidx.compose.foundation.layout.BoxScope  EdgeMagnificationEffect androidx.compose.material3  Paint androidx.compose.material3  apply androidx.compose.material3  EdgeMagnificationEffect androidx.compose.runtime  Paint androidx.compose.runtime  apply androidx.compose.runtime  	BlendMode androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  Clear &androidx.compose.ui.graphics.BlendMode  	Companion &androidx.compose.ui.graphics.BlendMode  Clear 0androidx.compose.ui.graphics.BlendMode.Companion  
drawCircle #androidx.compose.ui.graphics.Canvas  Color "androidx.compose.ui.graphics.Paint  androidx "androidx.compose.ui.graphics.Paint  apply "androidx.compose.ui.graphics.Paint  	blendMode "androidx.compose.ui.graphics.Paint  color "androidx.compose.ui.graphics.Paint  isAntiAlias "androidx.compose.ui.graphics.Paint  Paint 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  apply 0androidx.compose.ui.graphics.drawscope.DrawScope  drawIntoCanvas 0androidx.compose.ui.graphics.drawscope.DrawScope  EdgeMagnificationEffect com.example.zuijiji  Paint com.example.zuijiji  apply com.example.zuijiji  apply kotlin  	compareTo kotlin.Float  MultiLayerLensEffect "androidx.compose.foundation.layout  downTo "androidx.compose.foundation.layout  MultiLayerLensEffect +androidx.compose.foundation.layout.BoxScope  downTo +androidx.compose.foundation.layout.BoxScope  MultiLayerLensEffect androidx.compose.material3  downTo androidx.compose.material3  MultiLayerLensEffect androidx.compose.runtime  downTo androidx.compose.runtime  plus androidx.compose.ui.unit.Dp  times androidx.compose.ui.unit.Dp  MultiLayerLensEffect com.example.zuijiji  downTo com.example.zuijiji  dp kotlin.Float  downTo 
kotlin.ranges  TransparentBorderEffect "androidx.compose.foundation.layout  TransparentBorderEffect +androidx.compose.foundation.layout.BoxScope  TransparentBorderEffect androidx.compose.material3  TransparentBorderEffect androidx.compose.runtime  TransparentBorderEffect com.example.zuijiji  InnerLensEffect "androidx.compose.foundation.layout  InnerLensEffect +androidx.compose.foundation.layout.BoxScope  InnerLensEffect androidx.compose.material3  InnerLensEffect androidx.compose.runtime  InnerLensEffect com.example.zuijiji  
TopLensEffect "androidx.compose.foundation.layout  
TopLensEffect +androidx.compose.foundation.layout.BoxScope  
TopLensEffect androidx.compose.material3  
TopLensEffect androidx.compose.runtime  
TopLensEffect com.example.zuijiji  MultiLayerSmoothLensEffect "androidx.compose.foundation.layout  SmoothLensLayer "androidx.compose.foundation.layout  MultiLayerSmoothLensEffect +androidx.compose.foundation.layout.BoxScope  SmoothLensLayer +androidx.compose.foundation.layout.BoxScope  MultiLayerSmoothLensEffect androidx.compose.material3  SmoothLensLayer androidx.compose.material3  MultiLayerSmoothLensEffect androidx.compose.runtime  SmoothLensLayer androidx.compose.runtime  DstOut &androidx.compose.ui.graphics.BlendMode  DstOut 0androidx.compose.ui.graphics.BlendMode.Companion  Brush 0androidx.compose.ui.graphics.drawscope.DrawScope  radialGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  MultiLayerSmoothLensEffect com.example.zuijiji  SmoothLensLayer com.example.zuijiji  MultiLayerProgressiveLensEffect "androidx.compose.foundation.layout  
coerceAtLeast "androidx.compose.foundation.layout  MultiLayerProgressiveLensEffect +androidx.compose.foundation.layout.BoxScope  
coerceAtLeast +androidx.compose.foundation.layout.BoxScope  MultiLayerProgressiveLensEffect androidx.compose.material3  
coerceAtLeast androidx.compose.material3  MultiLayerProgressiveLensEffect androidx.compose.runtime  
coerceAtLeast androidx.compose.runtime  MultiLayerProgressiveLensEffect com.example.zuijiji  
coerceAtLeast com.example.zuijiji  
coerceAtLeast kotlin.Float  rem 
kotlin.Int  
coerceAtLeast 
kotlin.ranges  MidTopLensEffect "androidx.compose.foundation.layout  MidTopLensEffect +androidx.compose.foundation.layout.BoxScope  MidTopLensEffect androidx.compose.material3  MidTopLensEffect androidx.compose.runtime  MidTopLensEffect com.example.zuijiji  MidBottomLensEffect "androidx.compose.foundation.layout  MidBottomLensEffect +androidx.compose.foundation.layout.BoxScope  MidBottomLensEffect androidx.compose.material3  MidBottomLensEffect androidx.compose.runtime  MidBottomLensEffect com.example.zuijiji  Dp "androidx.compose.foundation.layout  NewMidLensEffect "androidx.compose.foundation.layout  NewMidLensEffect +androidx.compose.foundation.layout.BoxScope  Dp androidx.compose.material3  NewMidLensEffect androidx.compose.material3  Dp androidx.compose.runtime  NewMidLensEffect androidx.compose.runtime  Dp com.example.zuijiji  NewMidLensEffect com.example.zuijiji  SuperMidLensEffect "androidx.compose.foundation.layout  SuperMidLensEffect +androidx.compose.foundation.layout.BoxScope  SuperMidLensEffect androidx.compose.material3  SuperMidLensEffect androidx.compose.runtime  SuperMidLensEffect com.example.zuijiji  MidTopLensEffect2 "androidx.compose.foundation.layout  TopLensEffect2 "androidx.compose.foundation.layout  MidTopLensEffect2 +androidx.compose.foundation.layout.BoxScope  TopLensEffect2 +androidx.compose.foundation.layout.BoxScope  MidTopLensEffect2 androidx.compose.material3  TopLensEffect2 androidx.compose.material3  MidTopLensEffect2 androidx.compose.runtime  TopLensEffect2 androidx.compose.runtime  MidTopLensEffect2 com.example.zuijiji  TopLensEffect2 com.example.zuijiji  NewLensEffect196 "androidx.compose.foundation.layout  NewLensEffect197 "androidx.compose.foundation.layout  NewLensEffect198 "androidx.compose.foundation.layout  NewLensEffect199 "androidx.compose.foundation.layout  NewLensEffect196 +androidx.compose.foundation.layout.BoxScope  NewLensEffect197 +androidx.compose.foundation.layout.BoxScope  NewLensEffect198 +androidx.compose.foundation.layout.BoxScope  NewLensEffect199 +androidx.compose.foundation.layout.BoxScope  NewLensEffect196 androidx.compose.material3  NewLensEffect197 androidx.compose.material3  NewLensEffect198 androidx.compose.material3  NewLensEffect199 androidx.compose.material3  NewLensEffect196 androidx.compose.runtime  NewLensEffect197 androidx.compose.runtime  NewLensEffect198 androidx.compose.runtime  NewLensEffect199 androidx.compose.runtime  NewLensEffect196 com.example.zuijiji  NewLensEffect197 com.example.zuijiji  NewLensEffect198 com.example.zuijiji  NewLensEffect199 com.example.zuijiji  NewLensEffect191 "androidx.compose.foundation.layout  NewLensEffect192 "androidx.compose.foundation.layout  NewLensEffect193 "androidx.compose.foundation.layout  NewLensEffect194 "androidx.compose.foundation.layout  NewLensEffect195 "androidx.compose.foundation.layout  NewLensEffect191 +androidx.compose.foundation.layout.BoxScope  NewLensEffect192 +androidx.compose.foundation.layout.BoxScope  NewLensEffect193 +androidx.compose.foundation.layout.BoxScope  NewLensEffect194 +androidx.compose.foundation.layout.BoxScope  NewLensEffect195 +androidx.compose.foundation.layout.BoxScope  NewLensEffect191 androidx.compose.material3  NewLensEffect192 androidx.compose.material3  NewLensEffect193 androidx.compose.material3  NewLensEffect194 androidx.compose.material3  NewLensEffect195 androidx.compose.material3  NewLensEffect191 androidx.compose.runtime  NewLensEffect192 androidx.compose.runtime  NewLensEffect193 androidx.compose.runtime  NewLensEffect194 androidx.compose.runtime  NewLensEffect195 androidx.compose.runtime  NewLensEffect191 com.example.zuijiji  NewLensEffect192 com.example.zuijiji  NewLensEffect193 com.example.zuijiji  NewLensEffect194 com.example.zuijiji  NewLensEffect195 com.example.zuijiji  NewLensEffect181 "androidx.compose.foundation.layout  NewLensEffect182 "androidx.compose.foundation.layout  NewLensEffect183 "androidx.compose.foundation.layout  NewLensEffect184 "androidx.compose.foundation.layout  NewLensEffect185 "androidx.compose.foundation.layout  NewLensEffect186 "androidx.compose.foundation.layout  NewLensEffect187 "androidx.compose.foundation.layout  NewLensEffect188 "androidx.compose.foundation.layout  NewLensEffect189 "androidx.compose.foundation.layout  NewLensEffect181 +androidx.compose.foundation.layout.BoxScope  NewLensEffect182 +androidx.compose.foundation.layout.BoxScope  NewLensEffect183 +androidx.compose.foundation.layout.BoxScope  NewLensEffect184 +androidx.compose.foundation.layout.BoxScope  NewLensEffect185 +androidx.compose.foundation.layout.BoxScope  NewLensEffect186 +androidx.compose.foundation.layout.BoxScope  NewLensEffect187 +androidx.compose.foundation.layout.BoxScope  NewLensEffect188 +androidx.compose.foundation.layout.BoxScope  NewLensEffect189 +androidx.compose.foundation.layout.BoxScope  NewLensEffect181 androidx.compose.material3  NewLensEffect182 androidx.compose.material3  NewLensEffect183 androidx.compose.material3  NewLensEffect184 androidx.compose.material3  NewLensEffect185 androidx.compose.material3  NewLensEffect186 androidx.compose.material3  NewLensEffect187 androidx.compose.material3  NewLensEffect188 androidx.compose.material3  NewLensEffect189 androidx.compose.material3  NewLensEffect181 androidx.compose.runtime  NewLensEffect182 androidx.compose.runtime  NewLensEffect183 androidx.compose.runtime  NewLensEffect184 androidx.compose.runtime  NewLensEffect185 androidx.compose.runtime  NewLensEffect186 androidx.compose.runtime  NewLensEffect187 androidx.compose.runtime  NewLensEffect188 androidx.compose.runtime  NewLensEffect189 androidx.compose.runtime  NewLensEffect181 com.example.zuijiji  NewLensEffect182 com.example.zuijiji  NewLensEffect183 com.example.zuijiji  NewLensEffect184 com.example.zuijiji  NewLensEffect185 com.example.zuijiji  NewLensEffect186 com.example.zuijiji  NewLensEffect187 com.example.zuijiji  NewLensEffect188 com.example.zuijiji  NewLensEffect189 com.example.zuijiji  NewLensEffect171 "androidx.compose.foundation.layout  NewLensEffect172 "androidx.compose.foundation.layout  NewLensEffect173 "androidx.compose.foundation.layout  NewLensEffect174 "androidx.compose.foundation.layout  NewLensEffect176 "androidx.compose.foundation.layout  NewLensEffect177 "androidx.compose.foundation.layout  NewLensEffect178 "androidx.compose.foundation.layout  NewLensEffect179 "androidx.compose.foundation.layout  NewLensEffect180 "androidx.compose.foundation.layout  NewLensEffect171 +androidx.compose.foundation.layout.BoxScope  NewLensEffect172 +androidx.compose.foundation.layout.BoxScope  NewLensEffect173 +androidx.compose.foundation.layout.BoxScope  NewLensEffect174 +androidx.compose.foundation.layout.BoxScope  NewLensEffect176 +androidx.compose.foundation.layout.BoxScope  NewLensEffect177 +androidx.compose.foundation.layout.BoxScope  NewLensEffect178 +androidx.compose.foundation.layout.BoxScope  NewLensEffect179 +androidx.compose.foundation.layout.BoxScope  NewLensEffect180 +androidx.compose.foundation.layout.BoxScope  NewLensEffect171 androidx.compose.material3  NewLensEffect172 androidx.compose.material3  NewLensEffect173 androidx.compose.material3  NewLensEffect174 androidx.compose.material3  NewLensEffect176 androidx.compose.material3  NewLensEffect177 androidx.compose.material3  NewLensEffect178 androidx.compose.material3  NewLensEffect179 androidx.compose.material3  NewLensEffect180 androidx.compose.material3  NewLensEffect171 androidx.compose.runtime  NewLensEffect172 androidx.compose.runtime  NewLensEffect173 androidx.compose.runtime  NewLensEffect174 androidx.compose.runtime  NewLensEffect176 androidx.compose.runtime  NewLensEffect177 androidx.compose.runtime  NewLensEffect178 androidx.compose.runtime  NewLensEffect179 androidx.compose.runtime  NewLensEffect180 androidx.compose.runtime  NewLensEffect171 com.example.zuijiji  NewLensEffect172 com.example.zuijiji  NewLensEffect173 com.example.zuijiji  NewLensEffect174 com.example.zuijiji  NewLensEffect176 com.example.zuijiji  NewLensEffect177 com.example.zuijiji  NewLensEffect178 com.example.zuijiji  NewLensEffect179 com.example.zuijiji  NewLensEffect180 com.example.zuijiji  NewLensEffect161 "androidx.compose.foundation.layout  NewLensEffect162 "androidx.compose.foundation.layout  NewLensEffect163 "androidx.compose.foundation.layout  NewLensEffect164 "androidx.compose.foundation.layout  NewLensEffect166 "androidx.compose.foundation.layout  NewLensEffect167 "androidx.compose.foundation.layout  NewLensEffect168 "androidx.compose.foundation.layout  NewLensEffect169 "androidx.compose.foundation.layout  NewLensEffect161 +androidx.compose.foundation.layout.BoxScope  NewLensEffect162 +androidx.compose.foundation.layout.BoxScope  NewLensEffect163 +androidx.compose.foundation.layout.BoxScope  NewLensEffect164 +androidx.compose.foundation.layout.BoxScope  NewLensEffect166 +androidx.compose.foundation.layout.BoxScope  NewLensEffect167 +androidx.compose.foundation.layout.BoxScope  NewLensEffect168 +androidx.compose.foundation.layout.BoxScope  NewLensEffect169 +androidx.compose.foundation.layout.BoxScope  NewLensEffect161 androidx.compose.material3  NewLensEffect162 androidx.compose.material3  NewLensEffect163 androidx.compose.material3  NewLensEffect164 androidx.compose.material3  NewLensEffect166 androidx.compose.material3  NewLensEffect167 androidx.compose.material3  NewLensEffect168 androidx.compose.material3  NewLensEffect169 androidx.compose.material3  NewLensEffect161 androidx.compose.runtime  NewLensEffect162 androidx.compose.runtime  NewLensEffect163 androidx.compose.runtime  NewLensEffect164 androidx.compose.runtime  NewLensEffect166 androidx.compose.runtime  NewLensEffect167 androidx.compose.runtime  NewLensEffect168 androidx.compose.runtime  NewLensEffect169 androidx.compose.runtime  NewLensEffect161 com.example.zuijiji  NewLensEffect162 com.example.zuijiji  NewLensEffect163 com.example.zuijiji  NewLensEffect164 com.example.zuijiji  NewLensEffect166 com.example.zuijiji  NewLensEffect167 com.example.zuijiji  NewLensEffect168 com.example.zuijiji  NewLensEffect169 com.example.zuijiji  NewLensEffect201 "androidx.compose.foundation.layout  NewLensEffect202 "androidx.compose.foundation.layout  NewLensEffect203 "androidx.compose.foundation.layout  NewLensEffect204 "androidx.compose.foundation.layout  NewLensEffect205 "androidx.compose.foundation.layout  NewLensEffect206 "androidx.compose.foundation.layout  NewLensEffect207 "androidx.compose.foundation.layout  NewLensEffect208 "androidx.compose.foundation.layout  NewLensEffect209 "androidx.compose.foundation.layout  NewLensEffect210 "androidx.compose.foundation.layout  NewLensEffect201 +androidx.compose.foundation.layout.BoxScope  NewLensEffect202 +androidx.compose.foundation.layout.BoxScope  NewLensEffect203 +androidx.compose.foundation.layout.BoxScope  NewLensEffect204 +androidx.compose.foundation.layout.BoxScope  NewLensEffect205 +androidx.compose.foundation.layout.BoxScope  NewLensEffect206 +androidx.compose.foundation.layout.BoxScope  NewLensEffect207 +androidx.compose.foundation.layout.BoxScope  NewLensEffect208 +androidx.compose.foundation.layout.BoxScope  NewLensEffect209 +androidx.compose.foundation.layout.BoxScope  NewLensEffect210 +androidx.compose.foundation.layout.BoxScope  NewLensEffect201 androidx.compose.material3  NewLensEffect202 androidx.compose.material3  NewLensEffect203 androidx.compose.material3  NewLensEffect204 androidx.compose.material3  NewLensEffect205 androidx.compose.material3  NewLensEffect206 androidx.compose.material3  NewLensEffect207 androidx.compose.material3  NewLensEffect208 androidx.compose.material3  NewLensEffect209 androidx.compose.material3  NewLensEffect210 androidx.compose.material3  NewLensEffect201 androidx.compose.runtime  NewLensEffect202 androidx.compose.runtime  NewLensEffect203 androidx.compose.runtime  NewLensEffect204 androidx.compose.runtime  NewLensEffect205 androidx.compose.runtime  NewLensEffect206 androidx.compose.runtime  NewLensEffect207 androidx.compose.runtime  NewLensEffect208 androidx.compose.runtime  NewLensEffect209 androidx.compose.runtime  NewLensEffect210 androidx.compose.runtime  NewLensEffect201 com.example.zuijiji  NewLensEffect202 com.example.zuijiji  NewLensEffect203 com.example.zuijiji  NewLensEffect204 com.example.zuijiji  NewLensEffect205 com.example.zuijiji  NewLensEffect206 com.example.zuijiji  NewLensEffect207 com.example.zuijiji  NewLensEffect208 com.example.zuijiji  NewLensEffect209 com.example.zuijiji  NewLensEffect210 com.example.zuijiji  
FontWeight "androidx.compose.foundation.layout  Shadow "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  	TextStyle "androidx.compose.foundation.layout  
FontWeight +androidx.compose.foundation.layout.BoxScope  Shadow +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  	TextStyle +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  
FontWeight androidx.compose.material3  Shadow androidx.compose.material3  	TextAlign androidx.compose.material3  	TextStyle androidx.compose.material3  
FontWeight androidx.compose.runtime  Shadow androidx.compose.runtime  	TextAlign androidx.compose.runtime  	TextStyle androidx.compose.runtime  Shadow androidx.compose.ui.graphics  Bold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  
FontWeight com.example.zuijiji  Shadow com.example.zuijiji  	TextAlign com.example.zuijiji  	TextStyle com.example.zuijiji  MagnifierGlass "androidx.compose.foundation.layout  MagnifierGlass +androidx.compose.foundation.layout.BoxScope  MagnifierGlass androidx.compose.material3  MagnifierGlass androidx.compose.runtime  BottomCenter androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  Gray "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  coerceIn 3androidx.compose.ui.input.pointer.PointerInputScope  MagnifierGlass com.example.zuijiji  not kotlin.Boolean  with +androidx.compose.foundation.layout.BoxScope  MagnifierLayer "androidx.compose.foundation.layout  MagnifierLayer +androidx.compose.foundation.layout.BoxScope  MagnifierLayer androidx.compose.material3  MagnifierLayer androidx.compose.runtime  MagnifierLayer com.example.zuijiji  Shape androidx.compose.ui.graphics  CircleShape /androidx.compose.ui.graphics.GraphicsLayerScope  clip /androidx.compose.ui.graphics.GraphicsLayerScope  shape /androidx.compose.ui.graphics.GraphicsLayerScope  to "androidx.compose.foundation.layout  to +androidx.compose.foundation.layout.BoxScope  to androidx.compose.material3  to androidx.compose.runtime  to com.example.zuijiji  Pair kotlin  to kotlin  to kotlin.Float  TransformOrigin androidx.compose.ui.graphics  androidx /androidx.compose.ui.graphics.GraphicsLayerScope  transformOrigin /androidx.compose.ui.graphics.GraphicsLayerScope  toInt kotlin.Float  pow "androidx.compose.foundation.layout  pow +androidx.compose.foundation.layout.BoxScope  pow androidx.compose.material3  pow androidx.compose.runtime  pow /androidx.compose.ui.graphics.GraphicsLayerScope  sqrt /androidx.compose.ui.graphics.GraphicsLayerScope  translationX /androidx.compose.ui.graphics.GraphicsLayerScope  translationY /androidx.compose.ui.graphics.GraphicsLayerScope  pow 'androidx.compose.ui.layout.MeasureScope  pow 3androidx.compose.ui.layout.Placeable.PlacementScope  pow com.example.zuijiji  pow kotlin.Float  toFloat 
kotlin.Int                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      