1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.zuijiji"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:5:5-80
11-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:5:22-77
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:6:22-64
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bc1e5d916b5089273277ee0d7bd1bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.zuijiji.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bc1e5d916b5089273277ee0d7bd1bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bc1e5d916b5089273277ee0d7bd1bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.zuijiji.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bc1e5d916b5089273277ee0d7bd1bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bc1e5d916b5089273277ee0d7bd1bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:8:5-29:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7bc1e5d916b5089273277ee0d7bd1bd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:15:9-35
31        android:testOnly="true"
32        android:theme="@style/Theme.Zuijiji" >
32-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:16:9-45
33        <activity
33-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:18:9-28:20
34            android:name="com.example.zuijiji.MainActivity"
34-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:19:13-41
35            android:exported="true"
35-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:20:13-36
36            android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:21:13-45
37            android:theme="@style/Theme.Zuijiji" >
37-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:22:13-49
38            <intent-filter>
38-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:23:13-27:29
39                <action android:name="android.intent.action.MAIN" />
39-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:24:17-69
39-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:24:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:26:17-77
41-->C:\Users\<USER>\AndroidStudioProjects\zuijiji\app\src\main\AndroidManifest.xml:26:27-74
42            </intent-filter>
43        </activity>
44        <activity
44-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa2923957d3ed9e153a8e6581f08689\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
45            android:name="androidx.compose.ui.tooling.PreviewActivity"
45-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa2923957d3ed9e153a8e6581f08689\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
46            android:exported="true" />
46-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0aa2923957d3ed9e153a8e6581f08689\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
47        <activity
47-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb14307c630196a3dc7dd69f93098b0b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
48            android:name="androidx.activity.ComponentActivity"
48-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb14307c630196a3dc7dd69f93098b0b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
49            android:exported="true" />
49-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb14307c630196a3dc7dd69f93098b0b\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
50
51        <provider
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.example.zuijiji.androidx-startup"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b979b6764ea746107f37f6294c9ae8d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab1d25d4f6ebd6a76963523a007e1239\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab1d25d4f6ebd6a76963523a007e1239\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab1d25d4f6ebd6a76963523a007e1239\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <receiver
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
67            android:name="androidx.profileinstaller.ProfileInstallReceiver"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
68            android:directBootAware="false"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
69            android:enabled="true"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
70            android:exported="true"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
71            android:permission="android.permission.DUMP" >
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
73                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
76                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
79                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
82                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e7f0b8805018cb491d3a2100f32392\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
83            </intent-filter>
84        </receiver>
85    </application>
86
87</manifest>
