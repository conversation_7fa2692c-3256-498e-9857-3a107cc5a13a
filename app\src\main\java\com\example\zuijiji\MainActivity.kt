package com.example.zuijiji

import android.net.Uri
import android.os.Bundle
import android.webkit.MimeTypeMap
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.material3.*
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.zuijiji.ui.theme.ZuijijiTheme
import android.widget.VideoView
import android.media.MediaPlayer

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ZuijijiTheme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var showBlurDialog by remember { mutableStateOf(false) }
    var showParameterDialog by remember { mutableStateOf(false) }
    var dialogOffset by remember { mutableStateOf(Offset.Zero) }

    // 参数状态
    var dialogWidth by remember { mutableStateOf(320) }
    var dialogHeight by remember { mutableStateOf(145) }
    var blurRadius by remember { mutableStateOf(5f) }

    val mediaPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景内容层 - 当显示模糊弹窗时应用模糊效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .then(
                    if (showBlurDialog) Modifier.blur(blurRadius.dp)
                    else Modifier
                )
        ) {
            BackgroundLayer(
                backgroundImageUri = backgroundImageUri,
                mediaPickerLauncher = mediaPickerLauncher,
                onShowBlurDialog = { showBlurDialog = true },
                onShowParameterDialog = { showParameterDialog = true },
                dialogOffset = dialogOffset,
                showDialog = showBlurDialog
            )
        }

        // 毛玻璃弹窗 - 作为裁剪窗口
        if (showBlurDialog) {
            GlassMorphismDialog(
                onDismiss = { showBlurDialog = false },
                onOffsetChange = { dialogOffset = it },
                dialogWidth = dialogWidth,
                dialogHeight = dialogHeight,
                dialogOffset = dialogOffset
            )
        }

        // 参数调整弹窗
        if (showParameterDialog) {
            ParameterAdjustmentDialog(
                dialogWidth = dialogWidth,
                dialogHeight = dialogHeight,
                blurRadius = blurRadius,
                onWidthChange = { dialogWidth = it },
                onHeightChange = { dialogHeight = it },
                onBlurRadiusChange = { blurRadius = it },
                onDismiss = { showParameterDialog = false }
            )
        }
    }
}

@Composable
fun BackgroundLayer(
    backgroundImageUri: Uri?,
    mediaPickerLauncher: androidx.activity.result.ActivityResultLauncher<String>,
    onShowBlurDialog: () -> Unit,
    onShowParameterDialog: () -> Unit,
    dialogOffset: Offset,
    showDialog: Boolean
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景媒体（图片或视频）
        backgroundImageUri?.let { uri ->
            val context = LocalContext.current
            if (isVideoFile(context, uri)) {
                // 视频背景
                AndroidView(
                    factory = { ctx ->
                        VideoView(ctx).apply {
                            setVideoURI(uri)
                            setOnPreparedListener { mediaPlayer ->
                                mediaPlayer.isLooping = true
                                mediaPlayer.setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING)
                            }
                            start()
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // 图片背景
                Image(
                    painter = rememberAsyncImagePainter(
                        ImageRequest.Builder(context)
                            .data(uri)
                            .build()
                    ),
                    contentDescription = "背景图片",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
            }
        }

        // 控制按钮
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 左下角按钮
            Button(
                onClick = {
                    // 支持所有文件类型，让用户选择图片或视频
                    mediaPickerLauncher.launch("*/*")
                },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(24.dp)
            ) {
                Text("选择媒体")
            }

            // 底部中间按钮
            Button(
                onClick = onShowParameterDialog,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(24.dp)
            ) {
                Text("调整参数")
            }

            // 右下角按钮
            Button(
                onClick = onShowBlurDialog,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(24.dp)
            ) {
                Text("模糊效果")
            }
        }
    }
}

@Composable
fun GlassMorphismDialog(
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit,
    dialogWidth: Int,
    dialogHeight: Int,
    dialogOffset: Offset
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(dialogOffset) }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 毛玻璃卡片 - 作为透明窗口，显示后面模糊的内容
        Box(
            modifier = Modifier
                .width(dialogWidth.dp)
                .height(dialogHeight.dp)
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .clip(RoundedCornerShape(20.dp))
                .background(
                    Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.15f),
                            Color.White.copy(alpha = 0.05f)
                        )
                    )
                )
                .border(
                    width = 2.dp,
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.6f),
                            Color.White.copy(alpha = 0.2f)
                        )
                    ),
                    shape = RoundedCornerShape(20.dp)
                )
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        )
    }
}

@Composable
fun ParameterAdjustmentDialog(
    dialogWidth: Int,
    dialogHeight: Int,
    blurRadius: Float,
    onWidthChange: (Int) -> Unit,
    onHeightChange: (Int) -> Unit,
    onBlurRadiusChange: (Float) -> Unit,
    onDismiss: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .pointerInput(Unit) {
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        Card(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(32.dp)
                .width(300.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "调整参数",
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )

                // 宽度调整
                Text(text = "宽度: ${dialogWidth}dp")
                Slider(
                    value = dialogWidth.toFloat(),
                    onValueChange = { onWidthChange(it.toInt()) },
                    valueRange = 200f..500f,
                    steps = 29
                )

                // 高度调整
                Text(text = "高度: ${dialogHeight}dp")
                Slider(
                    value = dialogHeight.toFloat(),
                    onValueChange = { onHeightChange(it.toInt()) },
                    valueRange = 100f..400f,
                    steps = 29
                )

                // 模糊度调整
                Text(text = "模糊度: ${String.format("%.1f", blurRadius)}dp")
                Slider(
                    value = blurRadius,
                    onValueChange = onBlurRadiusChange,
                    valueRange = 0f..100f,
                    steps = 199
                )

                // 确认按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                ) {
                    Text("确认")
                }
            }
        }
    }
}

// 检测是否为视频文件
fun isVideoFile(context: android.content.Context, uri: Uri): Boolean {
    val contentResolver = context.contentResolver
    val mimeType = contentResolver.getType(uri)
    return mimeType?.startsWith("video/") == true
}