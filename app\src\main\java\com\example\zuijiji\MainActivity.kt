package com.example.zuijiji

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import kotlin.math.roundToInt
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.zuijiji.ui.theme.ZuijijiTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ZuijijiTheme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    var backgroundImageUri by remember { mutableStateOf<Uri?>(null) }
    var showBlurDialog by remember { mutableStateOf(false) }
    var showParameterDialog by remember { mutableStateOf(false) }
    var dialogOffset by remember { mutableStateOf(Offset.Zero) }

    // 参数状态
    var dialogWidth by remember { mutableStateOf(320) }
    var dialogHeight by remember { mutableStateOf(145) }
    var blurRadius by remember { mutableStateOf(5f) }

    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        backgroundImageUri = uri
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景内容层
        BackgroundLayer(
            backgroundImageUri = backgroundImageUri,
            imagePickerLauncher = imagePickerLauncher,
            onShowBlurDialog = { showBlurDialog = true },
            onShowParameterDialog = { showParameterDialog = true },
            dialogOffset = dialogOffset,
            showDialog = showBlurDialog
        )

        // 毛玻璃弹窗
        if (showBlurDialog) {
            RealTimeBlurDialog(
                backgroundImageUri = backgroundImageUri,
                onDismiss = { showBlurDialog = false },
                onOffsetChange = { dialogOffset = it },
                dialogWidth = dialogWidth,
                dialogHeight = dialogHeight,
                blurRadius = blurRadius
            )
        }

        // 参数调整弹窗
        if (showParameterDialog) {
            ParameterAdjustmentDialog(
                dialogWidth = dialogWidth,
                dialogHeight = dialogHeight,
                blurRadius = blurRadius,
                onWidthChange = { dialogWidth = it },
                onHeightChange = { dialogHeight = it },
                onBlurRadiusChange = { blurRadius = it },
                onDismiss = { showParameterDialog = false }
            )
        }
    }
}

@Composable
fun BackgroundLayer(
    backgroundImageUri: Uri?,
    imagePickerLauncher: androidx.activity.result.ActivityResultLauncher<String>,
    onShowBlurDialog: () -> Unit,
    onShowParameterDialog: () -> Unit,
    dialogOffset: Offset,
    showDialog: Boolean
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        backgroundImageUri?.let { uri ->
            Image(
                painter = rememberAsyncImagePainter(
                    ImageRequest.Builder(LocalContext.current)
                        .data(uri)
                        .build()
                ),
                contentDescription = "背景图片",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }

        // 控制按钮
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // 左下角按钮
            Button(
                onClick = { imagePickerLauncher.launch("image/*") },
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(24.dp)
            ) {
                Text("选择图片")
            }

            // 底部中间按钮
            Button(
                onClick = onShowParameterDialog,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(24.dp)
            ) {
                Text("调整参数")
            }

            // 右下角按钮
            Button(
                onClick = onShowBlurDialog,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(24.dp)
            ) {
                Text("模糊效果")
            }
        }
    }
}

@Composable
fun RealTimeBlurDialog(
    backgroundImageUri: Uri?,
    onDismiss: () -> Unit,
    onOffsetChange: (Offset) -> Unit,
    dialogWidth: Int,
    dialogHeight: Int,
    blurRadius: Float
) {
    // 拖动偏移状态
    var offset by remember { mutableStateOf(Offset.Zero) }

    // 获取屏幕尺寸
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidthPx = with(density) { configuration.screenWidthDp.dp.toPx() }
    val screenHeightPx = with(density) { configuration.screenHeightDp.dp.toPx() }

    // 通知父组件偏移变化
    LaunchedEffect(offset) {
        onOffsetChange(offset)
    }

    // 使用Box覆盖整个屏幕
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0f))
            .pointerInput(Unit) {
                // 点击空白区域关闭弹窗
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        // 毛玻璃效果的正方形弹窗
        // 外部容器，处理拖拽和边框
        Box(
            modifier = Modifier
                .width(dialogWidth.dp)
                .height(dialogHeight.dp)
                .offset { IntOffset(offset.x.roundToInt(), offset.y.roundToInt()) }
                .align(Alignment.Center)
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offset += dragAmount
                    }
                }
        ) {
            // 实时背景模糊层 - 重新渲染背景内容
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(20.dp))
            ) {
                // 背景图片 - 保持全屏尺寸，根据弹窗位置进行偏移
                backgroundImageUri?.let { uri ->
                    Image(
                        painter = rememberAsyncImagePainter(
                            ImageRequest.Builder(LocalContext.current)
                                .data(uri)
                                .build()
                        ),
                        contentDescription = "实时模糊背景",
                        colorFilter = ColorFilter.colorMatrix(
                            colorMatrix = ColorMatrix(
                                floatArrayOf(
                                    1.4f, 0f, 0f, 0f, -0.4f,  // 红色通道：保持对比度，但降低白色
                                    0f, 1.4f, 0f, 0f, -0.4f,  // 绿色通道：保持对比度，但降低白色
                                    0f, 0f, 1.4f, 0f, -0.4f,  // 蓝色通道：保持对比度，但降低白色
                                    0f, 0f, 0f, 1f, 0f       // 透明度通道
                                )
                            )
                        ),
                        modifier = Modifier
                            .layout { measurable, constraints ->
                                // 使用真实的屏幕尺寸
                                val screenWidth = screenWidthPx.roundToInt()
                                val screenHeight = screenHeightPx.roundToInt()
                                val placeable = measurable.measure(
                                    constraints.copy(
                                        minWidth = screenWidth,
                                        maxWidth = screenWidth,
                                        minHeight = screenHeight,
                                        maxHeight = screenHeight
                                    )
                                )
                                layout(constraints.maxWidth, constraints.maxHeight) {
                                    placeable.place(
                                        -offset.x.roundToInt() - (screenWidth - constraints.maxWidth) / 2,
                                        -offset.y.roundToInt() - (screenHeight - constraints.maxHeight) / 2
                                    )
                                }
                            }
                            .blur(blurRadius.dp), // 模糊效果
                        contentScale = ContentScale.Crop
                    )
                }

                // 半透明白色背景层（最上层）
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White.copy(alpha = 0.15f))
                        .clip(RoundedCornerShape(20.dp))
                )
            }
        }
    }
}

@Composable
fun ParameterAdjustmentDialog(
    dialogWidth: Int,
    dialogHeight: Int,
    blurRadius: Float,
    onWidthChange: (Int) -> Unit,
    onHeightChange: (Int) -> Unit,
    onBlurRadiusChange: (Float) -> Unit,
    onDismiss: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .pointerInput(Unit) {
                detectTapGestures {
                    onDismiss()
                }
            }
    ) {
        Card(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(32.dp)
                .width(300.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .padding(24.dp)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "调整参数",
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )

                // 宽度调整
                Text(text = "宽度: ${dialogWidth}dp")
                Slider(
                    value = dialogWidth.toFloat(),
                    onValueChange = { onWidthChange(it.toInt()) },
                    valueRange = 200f..500f,
                    steps = 29
                )

                // 高度调整
                Text(text = "高度: ${dialogHeight}dp")
                Slider(
                    value = dialogHeight.toFloat(),
                    onValueChange = { onHeightChange(it.toInt()) },
                    valueRange = 100f..400f,
                    steps = 29
                )

                // 模糊度调整
                Text(text = "模糊度: ${String.format("%.1f", blurRadius)}dp")
                Slider(
                    value = blurRadius,
                    onValueChange = onBlurRadiusChange,
                    valueRange = 0f..100f,
                    steps = 199
                )

                // 确认按钮
                Button(
                    onClick = onDismiss,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                ) {
                    Text("确认")
                }
            }
        }
    }
}